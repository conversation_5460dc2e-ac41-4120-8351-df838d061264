#include <errno.h>
#include <fcntl.h>
#include <getopt.h>
#include <signal.h>
#include <stdio.h>
#include <syslog.h>
#include <unistd.h>
#include <cstdint>
#include <cstring>
#include <fstream>
#include <iostream>
#include <stdexcept>
#include <string>
#include <sys/mman.h>
#include <sys/poll.h>
#include <sys/stat.h>
#include <X11/Xlib.h>
#include <X11/Xutil.h>
#include <xf86drm.h>
#include <xf86drmMode.h>

class GpuAgentException : public std::runtime_error {
public:
    explicit GpuAgentException(const std::string& message) : std::runtime_error(message) {}
};

class GpuAgent
{
public:
    GpuAgent(uint16_t width, uint16_t height, uint8_t fps = 25);
    ~GpuAgent();
    void set_capture_resolution();
    void set_drm();
    void init_drm_event();
    void start_capture();
    void free_resource();
    static void handle_page_flip(int fd, uint32_t sequence, uint32_t tv_sec, uint32_t tv_usec, void *user_data);

private:
    uint16_t width;
    uint16_t height;
    uint8_t fps;
    uint32_t frame_delay_us;
    Display *display;
    Window root_win;
    int drm_fd;
    drmModeRes *res;
    drmModeConnector *conn;
    void *fb_data;
    uint16_t fb_id;
    uint16_t crtc_id;
    drmEventContext drm_ctx;
    struct pollfd pfd;
    struct drm_mode_create_dumb create_dumb;

    uint32_t read_hex_number_from_file(const std::string &path);
    std::string get_drm_device_path();
};
