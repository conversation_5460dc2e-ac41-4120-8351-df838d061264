# GPU Agent

A high-performance screen capture and display forwarding tool for Linux virtual machines. GPU Agent captures the display from a physical graphics card and forwards it to virtual GPU devices (VirtIO-GPU or QXL) in real-time.

## Features

- **Real-time Screen Capture**: Captures X11 desktop content at configurable frame rates
- **Virtual GPU Support**: Compatible with VirtIO-GPU and QXL virtual graphics devices
- **DRM Integration**: Direct rendering manager integration for efficient GPU operations
- **Automatic Device Detection**: Automatically detects and configures virtual GPU devices
- **Signal Handling**: Graceful shutdown on SIGINT/SIGTERM signals
- **Error Handling**: Comprehensive error handling with syslog integration
- **Configurable FPS**: Adjustable frame rate from 1-100 FPS (default: 20 FPS)

## Use Cases

- **VM Display Forwarding**: Forward physical GPU display to virtual machines
- **Remote Desktop**: Enable high-performance graphics in virtualized environments
- **GPU Passthrough Alternative**: Provide graphics acceleration without full GPU passthrough

## Requirements

### System Requirements
- Linux operating system
- X11 display server
- DRM-capable graphics hardware
- Virtual GPU device (VirtIO-GPU or QXL)

### Dependencies
- **libdrm**: Direct Rendering Manager library
- **libX11**: X11 client library
- **libX11-dev**: X11 development headers

### Build Dependencies
- **g++**: C++ compiler with C++11 support
- **make**: Build system
- **pkg-config**: Package configuration tool

## Installation

### Install Dependencies (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install build-essential libdrm-dev libx11-dev pkg-config
```

### Install Dependencies (CentOS/RHEL/Fedora)
```bash
# CentOS/RHEL
sudo yum install gcc-c++ libdrm-devel libX11-devel

# Fedora
sudo dnf install gcc-c++ libdrm-devel libX11-devel
```

### Build from Source
```bash
git clone http://git.xcube.com/spice/gpu-agent.git
cd gpu-agent
g++ -o gpu-agent gpu-agent.cpp gpu-agent.hpp $(pkg-config --cflags --libs libdrm) -lX11
```

## Usage

### Basic Usage
```bash
# Run with default settings (20 FPS)
./gpu-agent
```

### Command Line Options
- `-f, --fps FPS`: Set capture frame rate (1-100, default: 20)
- `-h, --help`: Show help message

### Examples
```bash
# High performance mode (60 FPS)
./gpu-agent --fps 60

# Low latency mode (30 FPS)
./gpu-agent -f 30

# Power saving mode (10 FPS)
./gpu-agent --fps 10
```

## Configuration

### Supported Resolutions
- **Default**: 1920x1080
- **Fallback**: 1280x800 (if default not supported)
- **Auto-detection**: Automatically detects and uses available display modes

### Supported Virtual GPUs
- **VirtIO-GPU**: PCI ID 1af4:0010
- **QXL**: PCI ID 1b36:0100

## Architecture

### Core Components
1. **X11 Capture**: Captures desktop content using XGetImage
2. **DRM Interface**: Manages virtual GPU through Direct Rendering Manager
3. **Frame Buffer**: Creates and manages GPU frame buffers
4. **Event Handling**: Processes DRM page flip events

### Data Flow
```
X11 Desktop → XGetImage → Frame Buffer → DRM Page Flip → Virtual GPU → VM Display
```

## Troubleshooting

### Common Issues

**Permission Denied**
```bash
# Add user to video group
sudo usermod -a -G video $USER
# Logout and login again
```

**No Virtual GPU Found**
- Ensure VM has VirtIO-GPU or QXL device configured
- Check `/dev/dri/` directory exists and is accessible
- Verify virtual GPU drivers are loaded

**Display Not Updating**
- Check X11 DISPLAY environment variable
- Ensure X11 server is running
- Verify user has access to X11 display

### Debug Information
```bash
# Check system logs
journalctl -f | grep gpu-agent

# Check DRM devices
ls -la /dev/dri/

# Check virtual GPU info
lspci | grep -i vga
```

## Performance

### Optimization Tips
- **Lower FPS**: Reduce CPU usage with lower frame rates
- **Resolution**: Use lower resolutions for better performance
- **VM Resources**: Allocate sufficient CPU and memory to VM

### Benchmarks
- **1920x1080@30fps**: ~50% single CPU usage (4-core system)
- **1290x1080@15fps**: ~30% single CPU usage (4-core system)
- **Memory Usage**: ~50MB typical

## Service Installation

### Run as Systemd Service
Create service file `/etc/systemd/system/gpu-agent.service`:
```ini
[Unit]
Description=GPU Agent Display Forwarding Service
After=graphical-session.target

[Service]
Type=simple
User=root
Environment=DISPLAY=:0
ExecStart=/opt/xclient/bin/gpu-agent --fps 30
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

Enable and start the service:
```bash
sudo systemctl daemon-reload
sudo systemctl enable gpu-agent
sudo systemctl start gpu-agent
```

## Support

For issues and questions:
- Check the troubleshooting section
- Review system logs for error messages
- Ensure all dependencies are properly installed